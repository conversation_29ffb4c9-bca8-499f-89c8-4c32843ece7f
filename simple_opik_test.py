#!/usr/bin/env python3
"""
简单的 Opik 追踪测试

这是一个最小化的测试脚本，用于验证：
1. Opik 追踪是否正常工作
2. DeepSeek 模型是否可以正常调用
3. 基本的对话功能

使用方法：
python simple_opik_test.py
"""

import asyncio
import os

from agents import Agent, Runner
from agents import set_trace_processors
from agents.extensions.models.litellm_model import LitellmModel
from opik.integrations.openai.agents import OpikTracingProcessor


async def main():
    """主测试函数"""
    print("🧪 开始 Opik + DeepSeek 基础测试")
    print("-" * 50)
    
    # 1. 配置 Opik 追踪
    print("1️⃣ 配置 Opik 追踪...")
    
    # 设置 Opik API 密钥
    os.environ["OPIK_API_KEY"] = "tMZKe4QpHJlaCR7aKQh8KwuoR"
    
    # 配置追踪处理器
    set_trace_processors(processors=[OpikTracingProcessor()])
    print("✅ Opik 追踪配置完成")
    
    # 2. 配置 DeepSeek 模型
    print("\n2️⃣ 配置 DeepSeek 模型...")
    deepseek_api_key = "***********************************"
    
    # 创建 DeepSeek 模型实例
    deepseek_model = LitellmModel(
        model="deepseek/deepseek-chat",
        api_key=deepseek_api_key
    )
    print("✅ DeepSeek 模型配置完成")
    
    # 3. 创建智能助手
    print("\n3️⃣ 创建智能助手...")
    agent = Agent(
        name="测试助手",
        instructions="你是一个友好的中文助手，请用简体中文回答问题。回答要简洁明了。",
        model=deepseek_model
    )
    print("✅ 智能助手创建完成")
    
    # 4. 测试基本对话
    print("\n4️⃣ 测试基本对话...")
    
    test_questions = [
        "你好，请简单介绍一下你自己",
        "1+1等于几？",
        "请用一句话描述人工智能"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n📝 测试 {i}/3")
        print(f"👤 问题：{question}")
        
        try:
            result = await Runner.run(agent, question)
            print(f"🤖 回答：{result.final_output}")
            print("✅ 测试成功")
        except Exception as e:
            print(f"❌ 测试失败：{str(e)}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("📊 请查看 Opik 仪表板查看追踪数据")
    print("🌐 Opik 地址：https://www.comet.com/opik")
    print("=" * 50)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n💥 发生错误：{str(e)}")
        print("💡 请检查网络连接和配置")
