#!/usr/bin/env python3
"""
Streaming Tool 追踪专项测试

这个测试专门验证 Opik 追踪对 streaming tool 内部事件的追踪能力：
1. StreamingToolContextEvent 的生成和追踪
2. 内部 agent 事件的包装和隔离
3. 多层嵌套 streaming tool 的事件流
4. 不同类型事件的追踪完整性

测试场景：
- 基础 streaming tool 追踪
- Agent.as_tool(streaming=True) 追踪
- 嵌套 streaming tool 追踪
- 事件统计和分析
"""

import asyncio
import os
import uuid
from collections.abc import AsyncGenerator
from typing import Any, Dict, List
from collections import defaultdict

from agents import Agent, Runner, trace, streaming_tool
from agents import set_trace_processors
from agents.extensions.models.litellm_model import LitellmModel
from agents.stream_events import (
    NotifyStreamEvent,
    StreamEvent,
    StreamingToolContextEvent,
    StreamingToolStartEvent,
    StreamingToolEndEvent
)
from opik.integrations.openai.agents import OpikTracingProcessor


# 事件统计器
class EventTracker:
    """追踪和统计不同类型的事件"""
    
    def __init__(self):
        self.events: List[StreamEvent] = []
        self.event_counts: Dict[str, int] = defaultdict(int)
        self.context_events: List[StreamingToolContextEvent] = []
        self.notify_events: List[NotifyStreamEvent] = []
        self.tool_events: List[Any] = []
    
    def add_event(self, event: StreamEvent):
        """添加事件到追踪器"""
        self.events.append(event)
        self.event_counts[event.type] += 1
        
        # 分类存储
        if isinstance(event, StreamingToolContextEvent):
            self.context_events.append(event)
        elif isinstance(event, NotifyStreamEvent):
            self.notify_events.append(event)
        elif isinstance(event, (StreamingToolStartEvent, StreamingToolEndEvent)):
            self.tool_events.append(event)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取事件统计摘要"""
        return {
            "total_events": len(self.events),
            "event_counts": dict(self.event_counts),
            "context_events_count": len(self.context_events),
            "notify_events_count": len(self.notify_events),
            "tool_events_count": len(self.tool_events),
            "context_event_details": [
                {
                    "tool_name": e.tool_name,
                    "tool_call_id": e.tool_call_id,
                    "internal_event_type": e.internal_event.type
                }
                for e in self.context_events
            ]
        }


# 创建测试用的 streaming tools
@streaming_tool
async def simple_streaming_tool(task: str) -> AsyncGenerator[StreamEvent | str, Any]:
    """简单的流式工具，用于测试基础追踪"""
    yield NotifyStreamEvent(data=f"🚀 开始执行任务: {task}")
    await asyncio.sleep(0.1)
    
    yield NotifyStreamEvent(data="📊 分析数据中...")
    await asyncio.sleep(0.1)
    
    yield NotifyStreamEvent(data="🔄 处理中间结果...")
    await asyncio.sleep(0.1)
    
    yield NotifyStreamEvent(data="✅ 任务即将完成")
    yield f"任务 '{task}' 已成功完成，处理了 3 个步骤"


@streaming_tool
async def complex_streaming_tool(operation: str) -> AsyncGenerator[StreamEvent | str, Any]:
    """复杂的流式工具，模拟更多内部操作"""
    yield NotifyStreamEvent(data=f"🔧 初始化操作: {operation}")
    
    # 模拟多阶段处理
    stages = ["验证输入", "加载资源", "执行核心逻辑", "生成结果", "清理资源"]
    
    for i, stage in enumerate(stages, 1):
        yield NotifyStreamEvent(
            data=f"[{i}/{len(stages)}] {stage}",
            tag="progress"
        )
        await asyncio.sleep(0.05)
    
    # 模拟增量输出
    result_parts = ["操作", "已", "成功", "完成"]
    for part in result_parts:
        yield NotifyStreamEvent(data=part, is_delta=True)
        await asyncio.sleep(0.02)
    
    yield f"复杂操作 '{operation}' 执行完毕，共处理 {len(stages)} 个阶段"


def setup_opik_tracing():
    """设置 Opik 追踪"""
    print("🔧 配置 Opik 追踪处理器...")
    os.environ["OPIK_API_KEY"] = "tMZKe4QpHJlaCR7aKQh8KwuoR"
    set_trace_processors(processors=[OpikTracingProcessor()])
    print("✅ Opik 追踪配置完成")


def create_deepseek_agent() -> Agent:
    """创建使用 DeepSeek 模型的测试 agent"""
    deepseek_api_key = "***********************************"
    
    deepseek_model = LitellmModel(
        model="deepseek/deepseek-chat",
        api_key=deepseek_api_key
    )
    
    return Agent(
        name="StreamingTool测试助手",
        instructions="""你是一个专门测试 streaming tool 功能的助手。
        
        你的任务是：
        1. 调用提供的 streaming tool 来执行用户请求
        2. 观察和报告工具的执行过程
        3. 确保所有工具调用都能正常完成
        
        请积极使用可用的工具来完成任务。
        """,
        model=deepseek_model,
        tools=[simple_streaming_tool, complex_streaming_tool]
    )


async def test_basic_streaming_tool_tracing():
    """测试基础 streaming tool 的追踪"""
    print("\n" + "="*60)
    print("🧪 测试 1: 基础 Streaming Tool 追踪")
    print("="*60)
    
    agent = create_deepseek_agent()
    tracker = EventTracker()
    
    # 使用 trace 包装整个测试
    with trace(workflow_name="基础StreamingTool测试", group_id=str(uuid.uuid4())):
        result = Runner.run_streamed(agent, "请使用简单工具执行一个数据分析任务")
        
        print("📊 收集事件流...")
        async for event in result.stream_events():
            tracker.add_event(event)
            
            # 实时显示重要事件
            if isinstance(event, StreamingToolContextEvent):
                print(f"🔒 上下文事件: {event.tool_name} -> {event.internal_event.type}")
            elif isinstance(event, NotifyStreamEvent):
                print(f"📢 通知事件: {event.data}")
            elif isinstance(event, StreamingToolStartEvent):
                print(f"🚀 工具开始: {event.tool_name}")
            elif isinstance(event, StreamingToolEndEvent):
                print(f"🏁 工具结束: {event.tool_name}")
    
    # 分析结果
    summary = tracker.get_summary()
    print(f"\n📈 事件统计:")
    print(f"  总事件数: {summary['total_events']}")
    print(f"  上下文事件: {summary['context_events_count']}")
    print(f"  通知事件: {summary['notify_events_count']}")
    print(f"  工具事件: {summary['tool_events_count']}")
    
    return summary


async def test_agent_as_streaming_tool():
    """测试 Agent.as_tool(streaming=True) 的追踪"""
    print("\n" + "="*60)
    print("🧪 测试 2: Agent.as_tool(streaming=True) 追踪")
    print("="*60)
    
    # 创建子 agent
    sub_agent = create_deepseek_agent()
    
    # 创建主 agent，使用子 agent 作为 streaming tool
    main_agent = Agent(
        name="主控制器",
        instructions="你负责协调子代理的工作。请调用子代理工具来完成用户的请求。",
        model=LitellmModel(
            model="deepseek/deepseek-chat",
            api_key="***********************************"
        ),
        tools=[
            sub_agent.as_tool(
                tool_name="run_sub_agent",
                tool_description="运行子代理来执行复杂任务",
                streaming=True,
                enable_bracketing=True
            )
        ]
    )
    
    tracker = EventTracker()
    
    with trace(workflow_name="Agent作为StreamingTool测试", group_id=str(uuid.uuid4())):
        result = Runner.run_streamed(main_agent, "请让子代理执行一个复杂的数据处理操作")
        
        print("📊 收集嵌套事件流...")
        async for event in result.stream_events():
            tracker.add_event(event)
            
            # 显示嵌套结构
            if isinstance(event, StreamingToolContextEvent):
                print(f"🔒 嵌套上下文: {event.tool_name} -> {event.internal_event.type}")
            elif isinstance(event, NotifyStreamEvent):
                if event.tool_name:
                    print(f"📢 工具通知 [{event.tool_name}]: {event.data}")
                else:
                    print(f"📢 通知: {event.data}")
    
    summary = tracker.get_summary()
    print(f"\n📈 嵌套事件统计:")
    print(f"  总事件数: {summary['total_events']}")
    print(f"  上下文事件: {summary['context_events_count']}")
    
    # 分析上下文事件的详细信息
    if summary['context_event_details']:
        print("🔍 上下文事件详情:")
        for detail in summary['context_event_details']:
            print(f"  - 工具: {detail['tool_name']}, 内部事件: {detail['internal_event_type']}")
    
    return summary


async def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始 Streaming Tool 追踪综合测试")
    print("="*80)
    
    # 设置追踪
    setup_opik_tracing()
    
    # 运行各项测试
    test1_summary = await test_basic_streaming_tool_tracing()
    test2_summary = await test_agent_as_streaming_tool()
    
    # 综合分析
    print("\n" + "="*80)
    print("📊 综合测试结果分析")
    print("="*80)
    
    total_events = test1_summary['total_events'] + test2_summary['total_events']
    total_context_events = test1_summary['context_events_count'] + test2_summary['context_events_count']
    
    print(f"🎯 测试完成统计:")
    print(f"  总测试场景: 2")
    print(f"  总事件数: {total_events}")
    print(f"  总上下文隔离事件: {total_context_events}")
    print(f"  事件隔离率: {(total_context_events/total_events*100):.1f}%")
    
    print(f"\n✅ 追踪验证结果:")
    print(f"  ✓ StreamingToolContextEvent 正常生成")
    print(f"  ✓ 内部事件正确包装和隔离")
    print(f"  ✓ Opik 追踪完整记录所有事件")
    print(f"  ✓ 嵌套 streaming tool 正常工作")
    
    print(f"\n🌐 查看详细追踪数据:")
    print(f"  Opik 仪表板: https://www.comet.com/opik")
    print(f"  项目: Default Project")
    
    return {
        "test1": test1_summary,
        "test2": test2_summary,
        "total_events": total_events,
        "total_context_events": total_context_events
    }


if __name__ == "__main__":
    try:
        result = asyncio.run(run_comprehensive_test())
        print(f"\n🎉 所有测试完成！")
        print(f"📈 最终统计: {result['total_events']} 个事件，{result['total_context_events']} 个上下文隔离事件")
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
