#!/usr/bin/env python3
"""
Opik 追踪与 DeepSeek 模型集成演示

这个演示展示了如何：
1. 配置 Opik 追踪处理器
2. 使用 DeepSeek 模型作为第三方 LLM 提供商
3. 创建一个简单的智能助手
4. 追踪整个对话过程

运行前请确保：
- 已安装 opik 和 openai-agents 包
- 设置了 OPIK_API_KEY 环境变量或运行 `opik configure`
- 有有效的 DeepSeek API 密钥
"""

import asyncio
import os
import uuid
from typing import Any

from agents import Agent, Runner, function_tool, trace
from agents import set_trace_processors
from agents.extensions.models.litellm_model import LitellmModel
from opik.integrations.openai.agents import OpikTracingProcessor


# 配置 Opik 追踪
def setup_opik_tracing():
    """设置 Opik 追踪处理器"""
    print("🔧 正在配置 Opik 追踪...")
    
    # 设置 Opik 追踪处理器
    set_trace_processors(processors=[OpikTracingProcessor()])
    print("✅ Opik 追踪配置完成")


# 创建一些示例工具函数
@function_tool
def get_weather(city: str) -> str:
    """获取指定城市的天气信息
    
    Args:
        city: 城市名称
        
    Returns:
        天气信息字符串
    """
    print(f"🌤️ 正在获取 {city} 的天气信息...")
    # 模拟天气数据
    weather_data = {
        "北京": "晴天，温度 15°C，微风",
        "上海": "多云，温度 18°C，东南风",
        "深圳": "小雨，温度 22°C，南风",
        "广州": "阴天，温度 20°C，无风"
    }
    return weather_data.get(city, f"{city} 的天气：晴天，温度适宜")


@function_tool
def calculate_math(expression: str) -> str:
    """计算数学表达式
    
    Args:
        expression: 数学表达式（如 "2 + 3 * 4"）
        
    Returns:
        计算结果
    """
    print(f"🧮 正在计算：{expression}")
    try:
        # 安全的数学计算
        result = eval(expression, {"__builtins__": {}}, {})
        return f"计算结果：{expression} = {result}"
    except Exception as e:
        return f"计算错误：{str(e)}"


@function_tool
def get_current_time() -> str:
    """获取当前时间
    
    Returns:
        当前时间字符串
    """
    import datetime
    now = datetime.datetime.now()
    return f"当前时间：{now.strftime('%Y年%m月%d日 %H:%M:%S')}"


def create_deepseek_agent(api_key: str) -> Agent:
    """创建使用 DeepSeek 模型的智能助手
    
    Args:
        api_key: DeepSeek API 密钥
        
    Returns:
        配置好的 Agent 实例
    """
    print("🤖 正在创建 DeepSeek 智能助手...")
    
    # 使用 LiteLLM 配置 DeepSeek 模型
    deepseek_model = LitellmModel(
        model="deepseek/deepseek-chat",  # DeepSeek 聊天模型
        api_key=api_key
    )
    
    agent = Agent(
        name="DeepSeek智能助手",
        instructions="""你是一个友好、专业的中文智能助手。你的特点：
        
        1. 总是用简体中文回答问题
        2. 回答要准确、有用、简洁
        3. 当需要获取实时信息时，会主动使用可用的工具
        4. 对用户保持礼貌和耐心
        5. 如果不确定答案，会诚实地说明
        
        你可以使用以下工具：
        - 查询天气信息
        - 进行数学计算
        - 获取当前时间
        """,
        model=deepseek_model,
        tools=[get_weather, calculate_math, get_current_time]
    )
    
    print("✅ DeepSeek 智能助手创建完成")
    return agent


async def run_single_conversation(agent: Agent, user_input: str) -> str:
    """运行单次对话
    
    Args:
        agent: 智能助手实例
        user_input: 用户输入
        
    Returns:
        助手回复
    """
    print(f"\n👤 用户：{user_input}")
    
    result = await Runner.run(agent, user_input)
    response = result.final_output
    
    print(f"🤖 助手：{response}")
    return response


async def run_multi_turn_conversation(agent: Agent):
    """运行多轮对话演示
    
    Args:
        agent: 智能助手实例
    """
    print("\n🗣️ 开始多轮对话演示...")
    
    # 生成唯一的对话线程 ID
    thread_id = str(uuid.uuid4())
    print(f"📝 对话线程 ID：{thread_id}")
    
    # 使用 trace 包装整个对话，设置 group_id 用于线程追踪
    with trace(workflow_name="DeepSeek多轮对话", group_id=thread_id):
        # 第一轮对话
        result1 = await Runner.run(agent, "你好！请告诉我现在几点了？")
        print(f"\n👤 用户：你好！请告诉我现在几点了？")
        print(f"🤖 助手：{result1.final_output}")
        
        # 第二轮对话 - 基于上一轮的结果继续
        new_input = result1.to_input_list() + [
            {"role": "user", "content": "谢谢！那北京今天天气怎么样？"}
        ]
        result2 = await Runner.run(agent, new_input)
        print(f"\n👤 用户：谢谢！那北京今天天气怎么样？")
        print(f"🤖 助手：{result2.final_output}")
        
        # 第三轮对话 - 数学计算
        new_input = result2.to_input_list() + [
            {"role": "user", "content": "帮我计算一下 15 * 8 + 32 的结果"}
        ]
        result3 = await Runner.run(agent, new_input)
        print(f"\n👤 用户：帮我计算一下 15 * 8 + 32 的结果")
        print(f"🤖 助手：{result3.final_output}")


async def run_demo():
    """运行完整演示"""
    print("🚀 开始 Opik 追踪与 DeepSeek 模型集成演示")
    print("=" * 60)
    
    # 1. 设置 Opik 追踪
    setup_opik_tracing()
    
    # 2. 获取 DeepSeek API 密钥
    deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")
    if not deepseek_api_key:
        deepseek_api_key = "***********************************"  # 使用提供的密钥
    
    print(f"🔑 使用 DeepSeek API 密钥：{deepseek_api_key[:10]}...")
    
    # 3. 创建智能助手
    agent = create_deepseek_agent(deepseek_api_key)
    
    # 4. 运行单次对话演示
    print("\n" + "=" * 60)
    print("📋 单次对话演示")
    print("=" * 60)
    
    await run_single_conversation(agent, "你好，请介绍一下你自己")
    await run_single_conversation(agent, "深圳今天天气如何？")
    await run_single_conversation(agent, "计算 123 + 456 * 2")
    
    # 5. 运行多轮对话演示
    print("\n" + "=" * 60)
    print("🔄 多轮对话演示")
    print("=" * 60)
    
    await run_multi_turn_conversation(agent)
    
    print("\n" + "=" * 60)
    print("✅ 演示完成！")
    print("📊 请查看 Opik 仪表板以查看追踪数据")
    print("🌐 Opik 仪表板：https://www.comet.com/opik")
    print("=" * 60)


def main():
    """主函数"""
    try:
        # 检查 Opik 配置
        opik_api_key = os.getenv("OPIK_API_KEY")
        if not opik_api_key:
            print("⚠️ 警告：未检测到 OPIK_API_KEY 环境变量")
            print("💡 请运行 'opik configure' 或设置 OPIK_API_KEY 环境变量")
            print("🔧 使用提供的密钥：tMZKe4QpHJlaCR7aKQh8KwuoR")
            os.environ["OPIK_API_KEY"] = "tMZKe4QpHJlaCR7aKQh8KwuoR"
        
        # 运行异步演示
        asyncio.run(run_demo())
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误：{str(e)}")
        print("💡 请检查网络连接和 API 密钥配置")


if __name__ == "__main__":
    main()
