---
search:
  exclude: true
---
# 複数エージェントのオーケストレーション

オーケストレーションとは、アプリ内でエージェントがどのように流れるかを指します。どのエージェントが、どの順序で実行され、その後どう決定するかを制御します。エージェントをオーケストレーションする主な方法は次の 2 つです。

1.  LLM に判断させる:  LLM の知能を活用し、計画・推論を行い、その結果に基づいて次のステップを決定します。  
2.  コードでオーケストレーションする: コード側でエージェントの流れを定義します。

これらのパターンは組み合わせて使用できます。それぞれにトレードオフがあり、以下で説明します。

## LLM によるオーケストレーション

エージェントとは、 instructions、ツール、ハンドオフを備えた  LLM です。オープンエンドなタスクが与えられた場合、 LLM はタスクをどのように進めるかを自律的に計画し、ツールを使ってアクションやデータ取得を行い、ハンドオフでサブエージェントへタスクを委譲できます。たとえば、リサーチエージェントには次のようなツールを装備できます。

-   Web 検索でオンライン情報を取得する  
-   ファイル検索で独自データや接続を調べる  
-   コンピュータ操作でコンピュータ上のアクションを実行する  
-   コード実行でデータ分析を行う  
-   計画立案やレポート作成などに長けた専門エージェントへのハンドオフ  

このパターンはタスクがオープンエンドで、 LLM の知能に頼りたい場合に最適です。重要な戦術は次のとおりです。

1.  良いプロンプトに投資する。利用可能なツール、使い方、守るべきパラメーターを明確に示します。  
2.  アプリを監視し、改善を繰り返す。問題が起きた箇所を特定し、プロンプトを改善します。  
3.  エージェントに内省と改善を許可する。たとえばループで実行し自己批評させたり、エラーメッセージを渡して修正させたりします。  
4.  何でもこなす汎用エージェントより、特定タスクに特化したエージェントを用意します。  
5.  [evals](https://platform.openai.com/docs/guides/evals) に投資する。これによりエージェントを訓練し、タスク性能を向上できます。  

## コードによるオーケストレーション

LLM によるオーケストレーションは強力ですが、コードでオーケストレーションすると速度・コスト・性能の面でより決定的かつ予測可能になります。よく使われるパターンは次のとおりです。

-   [structured outputs](https://platform.openai.com/docs/guides/structured-outputs) を使って、コード側で検査できる 適切な形式のデータ を生成する。たとえばエージェントにタスクをいくつかのカテゴリーに分類させ、そのカテゴリーに応じて次のエージェントを選択します。  
-   あるエージェントの出力を次のエージェントの入力に変換して複数エージェントをチェーンする。ブログ記事執筆を「リサーチ → アウトライン作成 → 記事執筆 → 批評 → 改善」という一連のステップに分解できます。  
-   タスクを実行するエージェントを `while` ループで回し、評価とフィードバックを行うエージェントと組み合わせ、評価者が基準を満たしたと判断するまで繰り返します。  
-   `asyncio.gather` など Python の基本コンポーネントを用いて複数エージェントを並列実行する。互いに依存しない複数タスクがある場合に高速化できます。  

[`examples/agent_patterns`](https://github.com/openai/openai-agents-python/tree/main/examples/agent_patterns) には多数のコード例があります。