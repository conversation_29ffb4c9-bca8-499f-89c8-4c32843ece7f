# Opik 追踪与 DeepSeek 模型集成演示

这个演示展示了如何在 OpenAI Agents 项目中集成 Opik 追踪功能，并使用 DeepSeek 作为第三方 LLM 提供商。

## 📋 功能特性

- ✅ **Opik 追踪集成**：完整的对话追踪和性能监控
- ✅ **DeepSeek 模型支持**：使用 DeepSeek 作为 LLM 提供商
- ✅ **工具函数演示**：天气查询、数学计算、时间获取
- ✅ **多轮对话支持**：线程化对话追踪
- ✅ **中文界面**：完全中文化的用户体验

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的依赖：

```bash
# 项目已包含 opik 依赖，如果需要单独安装：
pip install opik>=1.8.6

# 确保 litellm 可用（用于 DeepSeek 集成）
pip install "openai-agents[litellm]"
```

### 2. 配置 API 密钥

演示中已经内置了测试用的 API 密钥，但你也可以使用自己的：

```bash
# Opik API 密钥（可选，演示中已内置）
export OPIK_API_KEY="你的_OPIK_API_密钥"

# DeepSeek API 密钥（可选，演示中已内置）
export DEEPSEEK_API_KEY="你的_DEEPSEEK_API_密钥"
```

或者运行 Opik 配置命令：

```bash
opik configure
```

### 3. 运行演示

#### 简单测试（推荐先运行）

```bash
python simple_opik_test.py
```

这个脚本会：
- 配置 Opik 追踪
- 创建 DeepSeek 智能助手
- 运行 3 个基本测试对话
- 验证所有功能正常工作

#### 完整演示

```bash
python demo_opik_deepseek.py
```

这个脚本包含：
- 单次对话演示
- 多轮对话演示
- 工具函数使用演示
- 完整的追踪功能展示

## 📊 查看追踪数据

运行演示后，你可以在以下地址查看追踪数据：

🌐 **Opik 仪表板**：https://www.comet.com/opik

在仪表板中你可以看到：
- 📈 对话流程图
- ⏱️ 响应时间统计
- 💰 Token 使用和成本分析
- 🔍 详细的请求/响应数据
- 🧵 多轮对话线程追踪

## 🛠️ 技术架构

### Opik 追踪配置

```python
from agents import set_trace_processors
from opik.integrations.openai.agents import OpikTracingProcessor

# 设置 Opik 追踪处理器
set_trace_processors(processors=[OpikTracingProcessor()])
```

### DeepSeek 模型配置

```python
from agents.extensions.models.litellm_model import LitellmModel

# 创建 DeepSeek 模型实例
deepseek_model = LitellmModel(
    model="deepseek/deepseek-chat",
    api_key="你的_API_密钥"
)
```

### 多轮对话追踪

```python
from agents import trace
import uuid

# 生成对话线程 ID
thread_id = str(uuid.uuid4())

# 使用 trace 包装对话，启用线程追踪
with trace(workflow_name="对话名称", group_id=thread_id):
    # 对话逻辑
    result = await Runner.run(agent, user_input)
```

## 🔧 自定义配置

### 使用自己的 API 密钥

1. **Opik API 密钥**：
   - 注册 [Comet Opik](https://www.comet.com/opik)
   - 获取 API 密钥
   - 设置环境变量或修改代码中的密钥

2. **DeepSeek API 密钥**：
   - 注册 [DeepSeek](https://platform.deepseek.com/)
   - 获取 API 密钥
   - 设置环境变量或修改代码中的密钥

### 添加自定义工具

```python
from agents import function_tool

@function_tool
def your_custom_tool(param: str) -> str:
    """你的自定义工具描述"""
    # 工具逻辑
    return "工具结果"

# 添加到 Agent
agent = Agent(
    name="助手",
    instructions="指令",
    model=model,
    tools=[your_custom_tool]  # 添加自定义工具
)
```

## 🐛 故障排除

### 常见问题

1. **网络连接错误**
   - 检查网络连接
   - 确认 API 密钥有效
   - 检查防火墙设置

2. **Opik 追踪不显示**
   - 确认 OPIK_API_KEY 设置正确
   - 检查 Opik 服务状态
   - 查看控制台错误信息

3. **DeepSeek 模型调用失败**
   - 验证 DEEPSEEK_API_KEY 有效性
   - 检查 API 配额和限制
   - 确认模型名称正确

### 调试模式

在代码中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 相关文档

- [OpenAI Agents 文档](https://openai.github.io/openai-agents-python/)
- [Opik 文档](https://www.comet.com/docs/opik)
- [DeepSeek API 文档](https://platform.deepseek.com/api-docs)
- [LiteLLM 文档](https://docs.litellm.ai/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个演示！

## 📄 许可证

本演示遵循项目的 MIT 许可证。
