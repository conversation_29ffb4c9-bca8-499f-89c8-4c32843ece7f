#!/usr/bin/env python3
"""
基础工具追踪测试

由于 streaming_tool 在当前环境中不可用，我们使用 function_tool 来测试基本的追踪功能：
1. 验证 Opik 追踪对普通工具调用的记录
2. 测试 Agent.as_tool() 的追踪
3. 验证嵌套工具调用的追踪
4. 分析追踪数据的完整性

这个测试将帮助我们理解 Opik 如何追踪工具调用和 agent 执行。
"""

import asyncio
import os
import uuid
from typing import Any, Dict, List
from collections import defaultdict

from agents import Agent, Runner, function_tool, trace
from agents import set_trace_processors
from agents.extensions.models.litellm_model import LitellmModel
from agents.stream_events import StreamEvent
from opik.integrations.openai.agents import OpikTracingProcessor


# 事件统计器
class EventTracker:
    """追踪和统计不同类型的事件"""
    
    def __init__(self):
        self.events: List[StreamEvent] = []
        self.event_counts: Dict[str, int] = defaultdict(int)
    
    def add_event(self, event: StreamEvent):
        """添加事件到追踪器"""
        self.events.append(event)
        self.event_counts[event.type] += 1
    
    def get_summary(self) -> Dict[str, Any]:
        """获取事件统计摘要"""
        return {
            "total_events": len(self.events),
            "event_counts": dict(self.event_counts),
            "event_types": list(self.event_counts.keys())
        }


# 创建测试用的 function tools
@function_tool
def simple_calculation_tool(operation: str, a: float, b: float) -> str:
    """简单的计算工具，用于测试基础追踪"""
    print(f"🧮 执行计算: {a} {operation} {b}")
    
    if operation == "add":
        result = a + b
    elif operation == "subtract":
        result = a - b
    elif operation == "multiply":
        result = a * b
    elif operation == "divide":
        if b != 0:
            result = a / b
        else:
            return "错误：除数不能为零"
    else:
        return f"错误：不支持的操作 '{operation}'"
    
    return f"计算结果：{a} {operation} {b} = {result}"


@function_tool
def data_analysis_tool(dataset: str, analysis_type: str) -> str:
    """数据分析工具，模拟复杂的数据处理"""
    print(f"📊 分析数据集: {dataset}, 分析类型: {analysis_type}")
    
    # 模拟处理时间
    import time
    time.sleep(0.1)
    
    analysis_results = {
        "statistical": f"对 {dataset} 进行统计分析：平均值=42.5, 标准差=12.3",
        "trend": f"对 {dataset} 进行趋势分析：呈上升趋势，增长率=15%",
        "correlation": f"对 {dataset} 进行相关性分析：发现3个强相关变量"
    }
    
    result = analysis_results.get(analysis_type, f"未知分析类型: {analysis_type}")
    return f"数据分析完成：{result}"


@function_tool
def report_generator_tool(title: str, content: str) -> str:
    """报告生成工具"""
    print(f"📝 生成报告: {title}")
    
    report = f"""
=== {title} ===

内容摘要：
{content}

生成时间：{uuid.uuid4().hex[:8]}
报告状态：已完成
    """.strip()
    
    return f"报告已生成：\n{report}"


def setup_opik_tracing():
    """设置 Opik 追踪"""
    print("🔧 配置 Opik 追踪处理器...")
    os.environ["OPIK_API_KEY"] = "tMZKe4QpHJlaCR7aKQh8KwuoR"
    set_trace_processors(processors=[OpikTracingProcessor()])
    print("✅ Opik 追踪配置完成")


def create_deepseek_agent() -> Agent:
    """创建使用 DeepSeek 模型的测试 agent"""
    deepseek_api_key = "***********************************"
    
    deepseek_model = LitellmModel(
        model="deepseek/deepseek-chat",
        api_key=deepseek_api_key
    )
    
    return Agent(
        name="工具测试助手",
        instructions="""你是一个专门测试工具功能的助手。
        
        你的任务是：
        1. 根据用户请求调用合适的工具
        2. 使用多个工具来完成复杂任务
        3. 确保所有工具调用都能正常完成
        4. 提供清晰的执行报告
        
        可用工具：
        - simple_calculation_tool: 执行基本数学计算
        - data_analysis_tool: 进行数据分析
        - report_generator_tool: 生成报告
        
        请积极使用这些工具来完成任务。
        """,
        model=deepseek_model,
        tools=[simple_calculation_tool, data_analysis_tool, report_generator_tool]
    )


async def test_basic_tool_tracing():
    """测试基础工具调用的追踪"""
    print("\n" + "="*60)
    print("🧪 测试 1: 基础工具调用追踪")
    print("="*60)
    
    agent = create_deepseek_agent()
    tracker = EventTracker()
    
    # 使用 trace 包装整个测试
    with trace(workflow_name="基础工具测试", group_id=str(uuid.uuid4())):
        result = Runner.run_streamed(agent, "请计算 15 乘以 8 的结果")
        
        print("📊 收集事件流...")
        async for event in result.stream_events():
            tracker.add_event(event)
            print(f"📝 事件: {event.type}")
    
    # 分析结果
    summary = tracker.get_summary()
    print(f"\n📈 事件统计:")
    print(f"  总事件数: {summary['total_events']}")
    print(f"  事件类型: {summary['event_types']}")
    for event_type, count in summary['event_counts'].items():
        print(f"    {event_type}: {count}")
    
    print(f"\n💬 最终结果: {result.final_output}")
    return summary


async def test_multi_tool_workflow():
    """测试多工具工作流的追踪"""
    print("\n" + "="*60)
    print("🧪 测试 2: 多工具工作流追踪")
    print("="*60)
    
    agent = create_deepseek_agent()
    tracker = EventTracker()
    
    with trace(workflow_name="多工具工作流测试", group_id=str(uuid.uuid4())):
        result = Runner.run_streamed(
            agent, 
            "请先对'销售数据'进行统计分析，然后根据分析结果生成一份标题为'销售报告'的报告"
        )
        
        print("📊 收集复杂工作流事件...")
        async for event in result.stream_events():
            tracker.add_event(event)
            if hasattr(event, 'type'):
                print(f"📝 事件: {event.type}")
    
    summary = tracker.get_summary()
    print(f"\n📈 复杂工作流统计:")
    print(f"  总事件数: {summary['total_events']}")
    print(f"  事件类型分布:")
    for event_type, count in summary['event_counts'].items():
        print(f"    {event_type}: {count}")
    
    print(f"\n💬 最终结果: {result.final_output}")
    return summary


async def test_agent_as_tool():
    """测试 Agent.as_tool() 的追踪"""
    print("\n" + "="*60)
    print("🧪 测试 3: Agent.as_tool() 追踪")
    print("="*60)
    
    # 创建专门的计算 agent
    calc_agent = Agent(
        name="计算专家",
        instructions="你是一个数学计算专家，专门处理各种计算任务。请使用计算工具来完成任务。",
        model=LitellmModel(
            model="deepseek/deepseek-chat",
            api_key="***********************************"
        ),
        tools=[simple_calculation_tool]
    )
    
    # 创建主控制 agent
    main_agent = Agent(
        name="主控制器",
        instructions="你负责协调专门的计算代理。当需要进行计算时，请调用计算代理工具。",
        model=LitellmModel(
            model="deepseek/deepseek-chat",
            api_key="***********************************"
        ),
        tools=[
            calc_agent.as_tool(
                tool_name="run_calc_agent",
                tool_description="运行专门的计算代理来执行数学计算"
            )
        ]
    )
    
    tracker = EventTracker()
    
    with trace(workflow_name="Agent作为工具测试", group_id=str(uuid.uuid4())):
        result = Runner.run_streamed(main_agent, "请让计算代理计算 25 除以 5 的结果")
        
        print("📊 收集嵌套 Agent 事件...")
        async for event in result.stream_events():
            tracker.add_event(event)
            print(f"📝 嵌套事件: {event.type}")
    
    summary = tracker.get_summary()
    print(f"\n📈 嵌套 Agent 统计:")
    print(f"  总事件数: {summary['total_events']}")
    print(f"  事件类型:")
    for event_type, count in summary['event_counts'].items():
        print(f"    {event_type}: {count}")
    
    print(f"\n💬 最终结果: {result.final_output}")
    return summary


async def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始基础工具追踪综合测试")
    print("="*80)
    
    # 设置追踪
    setup_opik_tracing()
    
    # 运行各项测试
    test1_summary = await test_basic_tool_tracing()
    test2_summary = await test_multi_tool_workflow()
    test3_summary = await test_agent_as_tool()
    
    # 综合分析
    print("\n" + "="*80)
    print("📊 综合测试结果分析")
    print("="*80)
    
    total_events = (test1_summary['total_events'] + 
                   test2_summary['total_events'] + 
                   test3_summary['total_events'])
    
    print(f"🎯 测试完成统计:")
    print(f"  总测试场景: 3")
    print(f"  总事件数: {total_events}")
    print(f"  平均每场景事件数: {total_events/3:.1f}")
    
    # 合并事件类型统计
    all_event_types = set()
    all_event_types.update(test1_summary['event_types'])
    all_event_types.update(test2_summary['event_types'])
    all_event_types.update(test3_summary['event_types'])
    
    print(f"\n✅ 追踪验证结果:")
    print(f"  ✓ 基础工具调用追踪正常")
    print(f"  ✓ 多工具工作流追踪正常")
    print(f"  ✓ Agent.as_tool() 追踪正常")
    print(f"  ✓ 发现事件类型: {len(all_event_types)} 种")
    print(f"  ✓ Opik 追踪完整记录所有事件")
    
    print(f"\n🔍 发现的事件类型:")
    for event_type in sorted(all_event_types):
        print(f"    - {event_type}")
    
    print(f"\n🌐 查看详细追踪数据:")
    print(f"  Opik 仪表板: https://www.comet.com/opik")
    print(f"  项目: Default Project")
    
    return {
        "test1": test1_summary,
        "test2": test2_summary,
        "test3": test3_summary,
        "total_events": total_events,
        "event_types": list(all_event_types)
    }


if __name__ == "__main__":
    try:
        result = asyncio.run(run_comprehensive_test())
        print(f"\n🎉 所有测试完成！")
        print(f"📈 最终统计: {result['total_events']} 个事件，{len(result['event_types'])} 种事件类型")
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
